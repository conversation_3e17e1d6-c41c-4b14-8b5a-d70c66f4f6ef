/* Modern Filter Form Styles - BranchList inspired */

.modern-filter-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  overflow: visible;
}

.filter-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
}

.filter-title {
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.filter-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0 0 1.5rem 0;
}

/* Score Input */
.score-input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.score-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.score-symbol {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
}

.score-value {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  width: 120px;
  outline: none;
}

.score-value::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.score-unit {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
}

.score-symbol-down {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
}

/* Categories */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.category-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.category-btn.selected {
  background: #4ecdc4;
  border-color: #4ecdc4;
  color: #1a1a1a;
}

/* Search Input and Dropdowns */
.dropdown-container {
  position: relative;
  z-index: 100;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: rgba(255, 255, 255, 0.6);
  z-index: 1;
}

.search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem 1rem 1rem 3rem;
  color: #ffffff;
  font-size: 1rem;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-input:focus {
  border-color: #4ecdc4;
  background: rgba(255, 255, 255, 0.15);
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(26, 26, 46, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
  z-index: 9999;
  backdrop-filter: blur(15px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
}

.dropdown-item {
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover:not(.disabled) {
  background: rgba(78, 205, 196, 0.2);
  color: #ffffff;
}

.dropdown-item.disabled {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

/* Custom Scrollbar for Dropdown */
.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.6);
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(78, 205, 196, 0.8);
}

/* Filter Row */
.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  position: relative;
}

.filter-row .filter-card:first-child .dropdown-container {
  z-index: 200;
}

.filter-row .filter-card:last-child .dropdown-container {
  z-index: 150;
}

@media (max-width: 768px) {
  .filter-row {
    grid-template-columns: 1fr;
  }
}

/* Branch Options */
.branch-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.selected-tag {
  background: #4ecdc4;
  color: #1a1a1a;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selected-tag:hover {
  background: #45b8b0;
  transform: translateY(-1px);
}

.branch-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.branch-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.branch-option input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #4ecdc4;
}

.branch-name {
  flex: 1;
}

/* Location Preferences */
.location-toggle {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.toggle-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-btn.active {
  background: #4ecdc4;
  border-color: #4ecdc4;
  color: #1a1a1a;
}

.add-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 44px;
}

.add-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.location-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.location-note {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modern-filter-form {
    padding: 0 0.5rem;
  }

  .filter-card {
    padding: 1.5rem;
  }

  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .score-value {
    font-size: 2rem;
    width: 100px;
  }
}
