/**
 * Data Service Layer - Abstracts data source for easy migration to Supabase
 * Currently handles CSV parsing but designed for future API integration
 */

import <PERSON> from "papaparse";

/**
 * College data model interface
 * @typedef {Object} College
 * @property {string} College_Code - Unique identifier for college
 * @property {string} College_Name - Name of the college
 * @property {string} Branch_Code - Unique identifier for branch
 * @property {string} Branch_Name - Name of the engineering branch
 * @property {string} Status - College status (Government, Un-Aided, etc.)
 * @property {string} Home_University - University affiliation
 * @property {string} Table_Context - Admission context
 * @property {string} Stage - Admission stage
 * @property {string} Category_Code - Category (GOPENS, GOBCS, etc.)
 * @property {number} Cutoff_Rank - Rank cutoff
 * @property {number} Merit_Percentile - Percentile cutoff
 */

class DataService {
  constructor() {
    this.colleges = [];
    this.isLoaded = false;
    this.isLoading = false;
    this.error = null;
  }

  /**
   * Load college data from CSV file
   * @returns {Promise<College[]>} Array of college objects
   */
  async loadCollegeData() {
    if (this.isLoaded) {
      return this.colleges;
    }

    if (this.isLoading) {
      // Wait for existing load to complete
      return new Promise((resolve, reject) => {
        const checkLoaded = () => {
          if (this.isLoaded) {
            resolve(this.colleges);
          } else if (this.error) {
            reject(this.error);
          } else {
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
      });
    }

    this.isLoading = true;
    this.error = null;

    try {
      // Import CSV file from public directory
      const csvUrl = "/structured_cutoffs_revised.csv";
      const response = await fetch(csvUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch CSV file: ${response.statusText}`);
      }

      const csvText = await response.text();

      return new Promise((resolve, reject) => {
        Papa.parse(csvText, {
          header: true,
          skipEmptyLines: true,
          transform: (value, field) => {
            // Convert numeric fields
            if (field === "Merit_Percentile" || field === "Cutoff_Rank") {
              const numValue = parseFloat(value);
              return isNaN(numValue) ? 0 : numValue;
            }
            return value ? value.trim() : "";
          },
          complete: (results) => {
            if (results.errors.length > 0) {
              console.warn("CSV parsing warnings:", results.errors);
            }

            this.colleges = results.data.filter(
              (college) =>
                college.College_Name &&
                college.Branch_Name &&
                college.Category_Code &&
                college.Merit_Percentile > 0
            );

            this.isLoaded = true;
            this.isLoading = false;
            resolve(this.colleges);
          },
          error: (error) => {
            this.error = new Error(`CSV parsing failed: ${error.message}`);
            this.isLoading = false;
            reject(this.error);
          },
        });
      });
    } catch (error) {
      this.error = error;
      this.isLoading = false;
      throw error;
    }
  }

  /**
   * Get unique values for filter dropdowns
   * @returns {Object} Object containing arrays of unique values
   */
  getFilterOptions() {
    if (!this.isLoaded) {
      return {
        categories: [],
        branches: [],
        universities: [],
        collegeTypes: [],
        stages: [],
        contexts: [],
      };
    }

    const options = {
      categories: [
        ...new Set(this.colleges.map((c) => c.Category_Code)),
      ].sort(),
      branches: [...new Set(this.colleges.map((c) => c.Branch_Name))].sort(),
      universities: [
        ...new Set(
          this.colleges
            .map((c) => c.Home_University)
            .filter((u) => u && u.trim() !== "")
        ),
      ].sort(),
      collegeTypes: [...new Set(this.colleges.map((c) => c.Status))].sort(),
      stages: [...new Set(this.colleges.map((c) => c.Stage))].sort(),
      contexts: [...new Set(this.colleges.map((c) => c.Table_Context))].sort(),
    };

    return options;
  }

  /**
   * Filter colleges based on criteria
   * @param {Object} filters - Filter criteria
   * @param {number} filters.minPercentile - Minimum percentile
   * @param {number} filters.maxPercentile - Maximum percentile
   * @param {string} filters.category - Selected category
   * @param {string} filters.branch - Selected branch
   * @param {string} filters.collegeType - Selected college type
   * @param {string} filters.searchTerm - Search term for college name
   * @returns {College[]} Filtered array of colleges
   */
  filterColleges(filters) {
    if (!this.isLoaded) {
      return [];
    }

    return this.colleges.filter((college) => {
      // Percentile range filter
      if (
        filters.minPercentile &&
        college.Merit_Percentile < filters.minPercentile
      ) {
        return false;
      }
      if (
        filters.maxPercentile &&
        college.Merit_Percentile > filters.maxPercentile
      ) {
        return false;
      }

      // Category filter
      if (filters.category && college.Category_Code !== filters.category) {
        return false;
      }

      // Branch filter
      if (filters.branch && college.Branch_Name !== filters.branch) {
        return false;
      }

      // College type filter
      if (filters.collegeType && college.Status !== filters.collegeType) {
        return false;
      }

      // Stage filter
      if (filters.stage && college.Stage !== filters.stage) {
        return false;
      }

      // Search term filter (college name)
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        if (!college.College_Name.toLowerCase().includes(searchLower)) {
          return false;
        }
      }

      // University filter
      if (
        filters.university &&
        college.Home_University !== filters.university
      ) {
        return false;
      }

      return true;
    });
  }

  /**
   * Sort colleges by specified field
   * @param {College[]} colleges - Array of colleges to sort
   * @param {string} sortBy - Field to sort by
   * @param {string} sortOrder - 'asc' or 'desc'
   * @returns {College[]} Sorted array of colleges
   */
  sortColleges(colleges, sortBy, sortOrder = "asc") {
    const sortedColleges = [...colleges];

    sortedColleges.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      // Handle numeric fields
      if (sortBy === "Merit_Percentile" || sortBy === "Cutoff_Rank") {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else {
        // Handle string fields
        aValue = String(aValue || "").toLowerCase();
        bValue = String(bValue || "").toLowerCase();
      }

      if (aValue < bValue) {
        return sortOrder === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortOrder === "asc" ? 1 : -1;
      }
      return 0;
    });

    return sortedColleges;
  }

  /**
   * Get paginated results
   * @param {College[]} colleges - Array of colleges
   * @param {number} page - Page number (1-based)
   * @param {number} pageSize - Number of items per page
   * @returns {Object} Paginated results with metadata
   */
  paginateResults(colleges, page = 1, pageSize = 15) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedColleges = colleges.slice(startIndex, endIndex);

    return {
      colleges: paginatedColleges,
      currentPage: page,
      totalPages: Math.ceil(colleges.length / pageSize),
      totalResults: colleges.length,
      hasNextPage: endIndex < colleges.length,
      hasPrevPage: page > 1,
    };
  }

  /**
   * Reset service state (useful for testing)
   */
  reset() {
    this.colleges = [];
    this.isLoaded = false;
    this.isLoading = false;
    this.error = null;
  }
}

// Export singleton instance
export default new DataService();
