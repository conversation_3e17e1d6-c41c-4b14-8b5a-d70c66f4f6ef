/**
 * College Results Component
 * Displays filtered college results in a modern interface
 */

import PropTypes from 'prop-types';
import CollegeTable from './CollegeTable';
import './CollegeResults.css';

/**
 * CollegeResults component
 * @param {Object} props - Component props
 * @param {Array} props.colleges - Array of college data
 * @param {number} props.totalResults - Total number of results
 * @param {Object} props.sortConfig - Current sort configuration
 * @param {Function} props.onSort - Sort handler
 * @param {number} props.currentPage - Current page number
 * @param {number} props.totalPages - Total number of pages
 * @param {boolean} props.hasNextPage - Whether there's a next page
 * @param {boolean} props.hasPrevPage - Whether there's a previous page
 * @param {Function} props.onPageChange - Page change handler
 * @param {Function} props.onNextPage - Next page handler
 * @param {Function} props.onPrevPage - Previous page handler
 * @param {Function} props.onBackToFilters - Back to filters handler
 * @param {Object} props.filters - Current filter values
 * @returns {JSX.Element} College results component
 */
const CollegeResults = ({
  colleges,
  totalResults,
  sortConfig,
  onSort,
  currentPage,
  totalPages,
  hasNextPage,
  hasPrevPage,
  onPageChange,
  onNextPage,
  onPrevPage,
  onBackToFilters,
  filters
}) => {
  return (
    <div className="college-results">
      <header className="results-header">
        <div className="header-content">
          <button 
            className="back-btn"
            onClick={onBackToFilters}
            aria-label="Back to filters"
          >
            ← Back
          </button>
          
          <div className="header-info">
            <h1 className="results-title">CollegeFinder</h1>
            <div className="results-summary">
              <span className="results-count">
                {totalResults} colleges found
              </span>
              {filters.minPercentile && (
                <span className="score-info">
                  for {filters.minPercentile}%ile score
                </span>
              )}
            </div>
          </div>
          
          <div className="filter-summary">
            {filters.category && (
              <span className="filter-tag">
                {filters.category}
              </span>
            )}
            {filters.branch && (
              <span className="filter-tag">
                {filters.branch.split(' ')[0]}...
              </span>
            )}
            {filters.searchTerm && (
              <span className="filter-tag">
                {filters.searchTerm.split(',')[0]}...
              </span>
            )}
          </div>
        </div>
      </header>

      <main className="results-main">
        <div className="results-container">
          {colleges.length === 0 ? (
            <div className="no-results">
              <div className="no-results-icon">🔍</div>
              <h3>No colleges found</h3>
              <p>Try adjusting your filters to see more results.</p>
              <button 
                className="adjust-filters-btn"
                onClick={onBackToFilters}
              >
                Adjust Filters
              </button>
            </div>
          ) : (
            <CollegeTable
              colleges={colleges}
              sortConfig={sortConfig}
              onSort={onSort}
              currentPage={currentPage}
              totalPages={totalPages}
              totalResults={totalResults}
              hasNextPage={hasNextPage}
              hasPrevPage={hasPrevPage}
              onPageChange={onPageChange}
              onNextPage={onNextPage}
              onPrevPage={onPrevPage}
            />
          )}
        </div>
      </main>

      <footer className="results-footer">
        <div className="footer-content">
          <p>© 2024 CollegeFinder. All rights reserved.</p>
          <p>Designed with ❤️ by an AI.</p>
        </div>
      </footer>
    </div>
  );
};

CollegeResults.propTypes = {
  colleges: PropTypes.array.isRequired,
  totalResults: PropTypes.number.isRequired,
  sortConfig: PropTypes.object.isRequired,
  onSort: PropTypes.func.isRequired,
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  hasNextPage: PropTypes.bool.isRequired,
  hasPrevPage: PropTypes.bool.isRequired,
  onPageChange: PropTypes.func.isRequired,
  onNextPage: PropTypes.func.isRequired,
  onPrevPage: PropTypes.func.isRequired,
  onBackToFilters: PropTypes.func.isRequired,
  filters: PropTypes.object.isRequired
};

export default CollegeResults;
