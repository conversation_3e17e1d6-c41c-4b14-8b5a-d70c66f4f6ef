/**
 * Custom hook for managing college data state and operations
 * Provides a clean interface for components to interact with college data
 */

import { useState, useEffect, useMemo } from 'react';
import dataService from '../services/dataService';

/**
 * Custom hook for college data management
 * @returns {Object} Hook state and methods
 */
export const useCollegeData = () => {
  const [colleges, setColleges] = useState([]);
  const [filteredColleges, setFilteredColleges] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    minPercentile: '',
    maxPercentile: '',
    category: '',
    branch: '',
    collegeType: '',
    searchTerm: ''
  });
  const [sortConfig, setSortConfig] = useState({
    field: 'Cutoff_Percentile',
    order: 'desc'
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(15);

  /**
   * Load initial college data
   */
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await dataService.loadCollegeData();
        setColleges(data);
      } catch (err) {
        setError(err.message);
        console.error('Failed to load college data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  /**
   * Apply filters and sorting whenever dependencies change
   */
  useEffect(() => {
    if (colleges.length === 0) {
      setFilteredColleges([]);
      return;
    }

    // Apply filters
    const filtered = dataService.filterColleges(filters);
    
    // Apply sorting
    const sorted = dataService.sortColleges(filtered, sortConfig.field, sortConfig.order);
    
    setFilteredColleges(sorted);
    setCurrentPage(1); // Reset to first page when filters change
  }, [colleges, filters, sortConfig]);

  /**
   * Get filter options for dropdowns
   */
  const filterOptions = useMemo(() => {
    return dataService.getFilterOptions();
  }, [colleges]);

  /**
   * Get paginated results
   */
  const paginatedResults = useMemo(() => {
    return dataService.paginateResults(filteredColleges, currentPage, pageSize);
  }, [filteredColleges, currentPage, pageSize]);

  /**
   * Update filters
   * @param {Object} newFilters - New filter values
   */
  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  /**
   * Clear all filters
   */
  const clearFilters = () => {
    setFilters({
      minPercentile: '',
      maxPercentile: '',
      category: '',
      branch: '',
      collegeType: '',
      searchTerm: ''
    });
  };

  /**
   * Update sort configuration
   * @param {string} field - Field to sort by
   */
  const updateSort = (field) => {
    setSortConfig(prev => ({
      field,
      order: prev.field === field && prev.order === 'asc' ? 'desc' : 'asc'
    }));
  };

  /**
   * Navigate to specific page
   * @param {number} page - Page number
   */
  const goToPage = (page) => {
    if (page >= 1 && page <= paginatedResults.totalPages) {
      setCurrentPage(page);
    }
  };

  /**
   * Navigate to next page
   */
  const nextPage = () => {
    if (paginatedResults.hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  };

  /**
   * Navigate to previous page
   */
  const prevPage = () => {
    if (paginatedResults.hasPrevPage) {
      setCurrentPage(prev => prev - 1);
    }
  };

  return {
    // Data
    colleges: paginatedResults.colleges,
    totalResults: paginatedResults.totalResults,
    
    // State
    isLoading,
    error,
    filters,
    sortConfig,
    
    // Pagination
    currentPage: paginatedResults.currentPage,
    totalPages: paginatedResults.totalPages,
    hasNextPage: paginatedResults.hasNextPage,
    hasPrevPage: paginatedResults.hasPrevPage,
    
    // Filter options
    filterOptions,
    
    // Actions
    updateFilters,
    clearFilters,
    updateSort,
    goToPage,
    nextPage,
    prevPage
  };
};
