/**
 * Reusable error message component
 * Displays error messages with optional retry functionality
 */

import PropTypes from 'prop-types';
import './ErrorMessage.css';

/**
 * ErrorMessage component
 * @param {Object} props - Component props
 * @param {string} props.message - Error message to display
 * @param {Function} props.onRetry - Optional retry callback function
 * @param {string} props.type - Error type ('error', 'warning', 'info')
 * @returns {JSX.Element} Error message component
 */
const ErrorMessage = ({ message, onRetry, type = 'error' }) => {
  return (
    <div className={`error-message error-message--${type}`}>
      <div className="error-content">
        <div className="error-icon">
          {type === 'error' && '⚠️'}
          {type === 'warning' && '⚠️'}
          {type === 'info' && 'ℹ️'}
        </div>
        <div className="error-text">
          <h3 className="error-title">
            {type === 'error' && 'Error'}
            {type === 'warning' && 'Warning'}
            {type === 'info' && 'Information'}
          </h3>
          <p className="error-description">{message}</p>
        </div>
      </div>
      {onRetry && (
        <button 
          className="retry-button"
          onClick={onRetry}
          type="button"
        >
          Try Again
        </button>
      )}
    </div>
  );
};

ErrorMessage.propTypes = {
  message: PropTypes.string.isRequired,
  onRetry: PropTypes.func,
  type: PropTypes.oneOf(['error', 'warning', 'info'])
};

export default ErrorMessage;
