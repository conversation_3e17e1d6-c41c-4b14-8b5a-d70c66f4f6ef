/**
 * Filter form component for college search
 * Provides input fields for filtering colleges by various criteria
 */

import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import './FilterForm.css';

/**
 * FilterForm component
 * @param {Object} props - Component props
 * @param {Object} props.filters - Current filter values
 * @param {Object} props.filterOptions - Available options for dropdowns
 * @param {Function} props.onFiltersChange - Callback for filter changes
 * @param {Function} props.onClearFilters - Callback to clear all filters
 * @returns {JSX.Element} Filter form component
 */
const FilterForm = ({ filters, filterOptions, onFiltersChange, onClearFilters }) => {
  const [localFilters, setLocalFilters] = useState(filters);
  const [errors, setErrors] = useState({});

  // Update local state when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  /**
   * Validate percentile input
   * @param {string} value - Input value
   * @param {string} field - Field name
   * @returns {string|null} Error message or null
   */
  const validatePercentile = (value, field) => {
    if (!value) return null;
    
    const num = parseFloat(value);
    if (isNaN(num)) {
      return 'Please enter a valid number';
    }
    if (num < 0 || num > 100) {
      return 'Percentile must be between 0 and 100';
    }
    
    // Check range consistency
    if (field === 'minPercentile' && localFilters.maxPercentile) {
      const maxNum = parseFloat(localFilters.maxPercentile);
      if (!isNaN(maxNum) && num > maxNum) {
        return 'Minimum percentile cannot be greater than maximum';
      }
    }
    if (field === 'maxPercentile' && localFilters.minPercentile) {
      const minNum = parseFloat(localFilters.minPercentile);
      if (!isNaN(minNum) && num < minNum) {
        return 'Maximum percentile cannot be less than minimum';
      }
    }
    
    return null;
  };

  /**
   * Handle input changes
   * @param {string} field - Field name
   * @param {string} value - New value
   */
  const handleInputChange = (field, value) => {
    const newFilters = { ...localFilters, [field]: value };
    setLocalFilters(newFilters);

    // Validate percentile fields
    if (field === 'minPercentile' || field === 'maxPercentile') {
      const error = validatePercentile(value, field);
      setErrors(prev => ({
        ...prev,
        [field]: error
      }));
      
      // Only update parent if no errors
      if (!error) {
        onFiltersChange({ [field]: value });
      }
    } else {
      // Update parent immediately for non-percentile fields
      onFiltersChange({ [field]: value });
    }
  };

  /**
   * Handle form submission
   * @param {Event} e - Form event
   */
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all percentile fields
    const minError = validatePercentile(localFilters.minPercentile, 'minPercentile');
    const maxError = validatePercentile(localFilters.maxPercentile, 'maxPercentile');
    
    const newErrors = {
      minPercentile: minError,
      maxPercentile: maxError
    };
    
    setErrors(newErrors);
    
    // If no errors, update filters
    if (!minError && !maxError) {
      onFiltersChange(localFilters);
    }
  };

  /**
   * Handle clear filters
   */
  const handleClearFilters = () => {
    setLocalFilters({
      minPercentile: '',
      maxPercentile: '',
      category: '',
      branch: '',
      collegeType: '',
      stage: '',
      searchTerm: ''
    });
    setErrors({});
    onClearFilters();
  };

  return (
    <form className="filter-form" onSubmit={handleSubmit}>
      <div className="filter-form__header">
        <h2>Filter Colleges</h2>
        <button 
          type="button" 
          className="clear-filters-btn"
          onClick={handleClearFilters}
        >
          Clear All
        </button>
      </div>

      <div className="filter-form__grid">
        {/* Search Term */}
        <div className="filter-group">
          <label htmlFor="searchTerm" className="filter-label">
            Search College Name
          </label>
          <input
            type="text"
            id="searchTerm"
            className="filter-input"
            placeholder="Enter college name..."
            value={localFilters.searchTerm}
            onChange={(e) => handleInputChange('searchTerm', e.target.value)}
          />
        </div>

        {/* Percentile Range */}
        <div className="filter-group">
          <label htmlFor="minPercentile" className="filter-label">
            Min Percentile
          </label>
          <input
            type="number"
            id="minPercentile"
            className={`filter-input ${errors.minPercentile ? 'filter-input--error' : ''}`}
            placeholder="e.g., 85"
            min="0"
            max="100"
            step="0.1"
            value={localFilters.minPercentile}
            onChange={(e) => handleInputChange('minPercentile', e.target.value)}
          />
          {errors.minPercentile && (
            <span className="filter-error">{errors.minPercentile}</span>
          )}
        </div>

        <div className="filter-group">
          <label htmlFor="maxPercentile" className="filter-label">
            Max Percentile
          </label>
          <input
            type="number"
            id="maxPercentile"
            className={`filter-input ${errors.maxPercentile ? 'filter-input--error' : ''}`}
            placeholder="e.g., 95"
            min="0"
            max="100"
            step="0.1"
            value={localFilters.maxPercentile}
            onChange={(e) => handleInputChange('maxPercentile', e.target.value)}
          />
          {errors.maxPercentile && (
            <span className="filter-error">{errors.maxPercentile}</span>
          )}
        </div>

        {/* Category */}
        <div className="filter-group">
          <label htmlFor="category" className="filter-label">
            Category
          </label>
          <select
            id="category"
            className="filter-select"
            value={localFilters.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
          >
            <option value="">All Categories</option>
            {filterOptions.categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Branch */}
        <div className="filter-group">
          <label htmlFor="branch" className="filter-label">
            Branch
          </label>
          <select
            id="branch"
            className="filter-select"
            value={localFilters.branch}
            onChange={(e) => handleInputChange('branch', e.target.value)}
          >
            <option value="">All Branches</option>
            {filterOptions.branches.map(branch => (
              <option key={branch} value={branch}>
                {branch}
              </option>
            ))}
          </select>
        </div>

        {/* College Status */}
        <div className="filter-group">
          <label htmlFor="collegeType" className="filter-label">
            College Status
          </label>
          <select
            id="collegeType"
            className="filter-select"
            value={localFilters.collegeType}
            onChange={(e) => handleInputChange('collegeType', e.target.value)}
          >
            <option value="">All Status</option>
            {filterOptions.collegeTypes.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>

        {/* Stage */}
        <div className="filter-group">
          <label htmlFor="stage" className="filter-label">
            Stage
          </label>
          <select
            id="stage"
            className="filter-select"
            value={localFilters.stage || ''}
            onChange={(e) => handleInputChange('stage', e.target.value)}
          >
            <option value="">All Stages</option>
            {filterOptions.stages.map(stage => (
              <option key={stage} value={stage}>
                {stage}
              </option>
            ))}
          </select>
        </div>
      </div>
    </form>
  );
};

FilterForm.propTypes = {
  filters: PropTypes.object.isRequired,
  filterOptions: PropTypes.object.isRequired,
  onFiltersChange: PropTypes.func.isRequired,
  onClearFilters: PropTypes.func.isRequired
};

export default FilterForm;
