/**
 * Modern Filter Form Component
 * Inspired by BranchList design with dark theme and modern UI
 */

import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import './ModernFilterForm.css';

/**
 * ModernFilterForm component
 * @param {Object} props - Component props
 * @param {Object} props.filters - Current filter values
 * @param {Object} props.filterOptions - Available options for dropdowns
 * @param {Function} props.onFiltersChange - Callback for filter changes
 * @param {Function} props.onClearFilters - Callback to clear all filters
 * @returns {JSX.Element} Modern filter form component
 */
const ModernFilterForm = ({ filters, filterOptions, onFiltersChange, onClearFilters }) => {
  const [localFilters, setLocalFilters] = useState(filters);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedBranches, setSelectedBranches] = useState([]);
  const [selectedLocations, setSelectedLocations] = useState([]);
  const [selectedUniversity, setSelectedUniversity] = useState('');
  const [showLocationPrefs, setShowLocationPrefs] = useState(false);
  const [showUniversityDropdown, setShowUniversityDropdown] = useState(false);
  const [showBranchDropdown, setShowBranchDropdown] = useState(false);
  const [universitySearch, setUniversitySearch] = useState('');
  const [branchSearch, setBranchSearch] = useState('');

  // Update local state when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  /**
   * Handle score input changes
   */
  const handleScoreChange = (value) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0 && numValue <= 100) {
      setLocalFilters(prev => ({ ...prev, minPercentile: value }));
      onFiltersChange({ minPercentile: value });
    }
  };

  /**
   * Handle category selection
   */
  const handleCategoryToggle = (category) => {
    const newCategories = selectedCategories.includes(category)
      ? selectedCategories.filter(c => c !== category)
      : [...selectedCategories, category];
    
    setSelectedCategories(newCategories);
    onFiltersChange({ category: newCategories[0] || '' }); // For now, use first selected
  };

  /**
   * Handle branch selection
   */
  const handleBranchToggle = (branch) => {
    const newBranches = selectedBranches.includes(branch)
      ? selectedBranches.filter(b => b !== branch)
      : [...selectedBranches, branch];
    
    setSelectedBranches(newBranches);
    onFiltersChange({ branch: newBranches[0] || '' }); // For now, use first selected
  };

  /**
   * Handle location selection
   */
  const handleLocationToggle = (location) => {
    const newLocations = selectedLocations.includes(location)
      ? selectedLocations.filter(l => l !== location)
      : [...selectedLocations, location];
    
    setSelectedLocations(newLocations);
  };

  /**
   * Handle university selection
   */
  const handleUniversitySelect = (university) => {
    setSelectedUniversity(university);
    setUniversitySearch(university);
    setShowUniversityDropdown(false);
    onFiltersChange({ university: university });
  };

  /**
   * Handle university search input
   */
  const handleUniversitySearchChange = (value) => {
    setUniversitySearch(value);
    setShowUniversityDropdown(true);
    // Don't update filters until selection is made
  };

  /**
   * Handle branch search input
   */
  const handleBranchSearchChange = (value) => {
    setBranchSearch(value);
    setShowBranchDropdown(true);
  };

  // Popular categories for quick selection
  const popularCategories = ['GOPENS', 'GOBCS', 'GOSTS', 'EWS', 'TFWS'];
  const availableCategories = filterOptions.categories.filter(cat =>
    popularCategories.includes(cat)
  );

  // Close dropdowns when clicking outside
  const handleClickOutside = (e) => {
    if (!e.target.closest('.dropdown-container')) {
      setShowUniversityDropdown(false);
      setShowBranchDropdown(false);
    }
  };

  // Add event listener for clicking outside
  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);



  return (
    <div className="modern-filter-form">
      {/* Score Input */}
      <div className="filter-card">
        <h3 className="filter-title">What's your score?</h3>
        <div className="score-input-container">
          <div className="score-input">
            <span className="score-symbol">^</span>
            <input
              type="number"
              className="score-value"
              value={localFilters.minPercentile}
              onChange={(e) => handleScoreChange(e.target.value)}
              placeholder="85.25"
              min="0"
              max="100"
              step="0.01"
            />
            <span className="score-unit">%ile</span>
          </div>
          <span className="score-symbol-down">v</span>
        </div>
      </div>

      {/* Categories */}
      <div className="filter-card">
        <h3 className="filter-title">Select categories you fit in</h3>
        <div className="category-grid">
          {availableCategories.map(category => (
            <button
              key={category}
              className={`category-btn ${selectedCategories.includes(category) ? 'selected' : ''}`}
              onClick={() => handleCategoryToggle(category)}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Home University */}
      <div className="filter-card">
        <h3 className="filter-title">Select your Home University</h3>
        <p className="filter-subtitle">Under which you passed CET</p>
        <div className="dropdown-container">
          <div className="search-input-container">
            <span className="search-icon">🔍</span>
            <input
              type="text"
              className="search-input"
              placeholder="Search universities..."
              value={universitySearch}
              onChange={(e) => handleUniversitySearchChange(e.target.value)}
              onFocus={() => setShowUniversityDropdown(true)}
            />
          </div>
          {showUniversityDropdown && (
            <div className="dropdown-menu">
              {filterOptions.universities && filterOptions.universities.length > 0 ? (
                <>
                  {filterOptions.universities
                    .filter(university =>
                      university.toLowerCase().includes(universitySearch.toLowerCase())
                    )
                    .slice(0, 15)
                    .map(university => (
                      <div
                        key={university}
                        className="dropdown-item"
                        onClick={() => handleUniversitySelect(university)}
                      >
                        {university}
                      </div>
                    ))
                  }
                  {filterOptions.universities
                    .filter(university =>
                      university.toLowerCase().includes(universitySearch.toLowerCase())
                    ).length === 0 && (
                    <div className="dropdown-item disabled">
                      No universities found
                    </div>
                  )}
                </>
              ) : (
                <div className="dropdown-item disabled">
                  Loading universities...
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Branches and Location in a row */}
      <div className="filter-row">
        {/* Preferred Branches */}
        <div className="filter-card">
          <h3 className="filter-title">Select preferred branches</h3>
          <div className="branch-tags">
            {selectedBranches.map(branch => (
              <span key={branch} className="selected-tag" onClick={() => handleBranchToggle(branch)}>
                {branch.length > 15 ? branch.substring(0, 15) + '...' : branch} ✕
              </span>
            ))}
          </div>
          <div className="dropdown-container">
            <div className="search-input-container">
              <span className="search-icon">🔍</span>
              <input
                type="text"
                className="search-input"
                placeholder="Search branches..."
                value={branchSearch}
                onChange={(e) => handleBranchSearchChange(e.target.value)}
                onFocus={() => setShowBranchDropdown(true)}
              />
            </div>
            {showBranchDropdown && (
              <div className="dropdown-menu">
                {filterOptions.branches && filterOptions.branches.length > 0 ? (
                  <>
                    {filterOptions.branches
                      .filter(branch =>
                        branch.toLowerCase().includes(branchSearch.toLowerCase()) &&
                        !selectedBranches.includes(branch)
                      )
                      .slice(0, 12)
                      .map(branch => (
                        <div
                          key={branch}
                          className="dropdown-item"
                          onClick={() => {
                            handleBranchToggle(branch);
                            setBranchSearch('');
                            setShowBranchDropdown(false);
                          }}
                        >
                          {branch}
                        </div>
                      ))
                    }
                    {filterOptions.branches
                      .filter(branch =>
                        branch.toLowerCase().includes(branchSearch.toLowerCase()) &&
                        !selectedBranches.includes(branch)
                      ).length === 0 && (
                      <div className="dropdown-item disabled">
                        No branches found
                      </div>
                    )}
                  </>
                ) : (
                  <div className="dropdown-item disabled">
                    Loading branches...
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Location Preferences */}
        <div className="filter-card">
          <h3 className="filter-title">Do you have any location preferences?</h3>
          <div className="location-toggle">
            <button
              className={`toggle-btn ${!showLocationPrefs ? 'active' : ''}`}
              onClick={() => setShowLocationPrefs(false)}
            >
              No
            </button>
            <button
              className={`toggle-btn ${showLocationPrefs ? 'active' : ''}`}
              onClick={() => setShowLocationPrefs(true)}
            >
              Yes
            </button>
            <button className="add-btn">+</button>
          </div>
          
          {showLocationPrefs && (
            <div className="location-tags">
              {selectedLocations.map(location => (
                <span key={location} className="selected-tag">
                  {location} ✕
                </span>
              ))}
              <span className="selected-tag">Pune ✕</span>
              <span className="selected-tag">Mumbai ✕</span>
            </div>
          )}
          
          <p className="location-note">
            More options coming soon!<br />
            💡 Consider nearby cities for more choices.
          </p>
        </div>
      </div>
    </div>
  );
};

ModernFilterForm.propTypes = {
  filters: PropTypes.object.isRequired,
  filterOptions: PropTypes.object.isRequired,
  onFiltersChange: PropTypes.func.isRequired,
  onClearFilters: PropTypes.func.isRequired
};

export default ModernFilterForm;
