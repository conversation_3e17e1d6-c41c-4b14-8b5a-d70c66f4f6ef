/**
 * Main College Finder Application
 * Provides a comprehensive interface for discovering engineering colleges
 * based on entrance exam performance and preferences
 */

import { useCollegeData } from './hooks/useCollegeData';
import FilterForm from './components/FilterForm';
import CollegeTable from './components/CollegeTable';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorMessage from './components/ErrorMessage';
import './App.css';

/**
 * Main App component
 * @returns {JSX.Element} College Finder application
 */
function App() {
  const {
    colleges,
    totalResults,
    isLoading,
    error,
    filters,
    sortConfig,
    currentPage,
    totalPages,
    hasNextPage,
    hasPrevPage,
    filterOptions,
    updateFilters,
    clearFilters,
    updateSort,
    goToPage,
    nextPage,
    prevPage
  } = useCollegeData();

  /**
   * Handle retry when data loading fails
   */
  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div className="app">
      <header className="app-header">
        <div className="container">
          <h1 className="app-title">🎓 College Finder</h1>
          <p className="app-subtitle">
            Discover the perfect engineering college based on your entrance exam performance
          </p>
        </div>
      </header>

      <main className="app-main">
        <div className="container">
          {error ? (
            <ErrorMessage
              message={error}
              onRetry={handleRetry}
              type="error"
            />
          ) : (
            <>
              <FilterForm
                filters={filters}
                filterOptions={filterOptions}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
              />

              {isLoading ? (
                <LoadingSpinner
                  message="Loading college data..."
                  size="large"
                />
              ) : (
                <CollegeTable
                  colleges={colleges}
                  sortConfig={sortConfig}
                  onSort={updateSort}
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalResults={totalResults}
                  hasNextPage={hasNextPage}
                  hasPrevPage={hasPrevPage}
                  onPageChange={goToPage}
                  onNextPage={nextPage}
                  onPrevPage={prevPage}
                />
              )}
            </>
          )}
        </div>
      </main>

      <footer className="app-footer">
        <div className="container">
          <p>&copy; 2024 College Finder. Built with React + Vite.</p>
        </div>
      </footer>
    </div>
  );
}

export default App;
