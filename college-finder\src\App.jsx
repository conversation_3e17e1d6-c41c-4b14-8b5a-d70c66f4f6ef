/**
 * Main College Finder Application
 * Modern UI inspired by BranchList design
 */

import { useState } from 'react';
import { useCollegeData } from './hooks/useCollegeData';
import ModernFilterForm from './components/ModernFilterForm';
import CollegeResults from './components/CollegeResults';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorMessage from './components/ErrorMessage';
import './App.css';

/**
 * Main App component
 * @returns {JSX.Element} College Finder application
 */
function App() {
  const [showResults, setShowResults] = useState(false);
  const {
    colleges,
    totalResults,
    isLoading,
    error,
    filters,
    sortConfig,
    currentPage,
    totalPages,
    hasNextPage,
    hasPrevPage,
    filterOptions,
    updateFilters,
    clearFilters,
    updateSort,
    goToPage,
    nextPage,
    prevPage
  } = useCollegeData();

  /**
   * <PERSON>le getting college list
   */
  const handleGetList = () => {
    setShowResults(true);
  };

  /**
   * <PERSON>le going back to filters
   */
  const handleBackToFilters = () => {
    setShowResults(false);
  };

  /**
   * Handle retry when data loading fails
   */
  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div className="app">
      <div className="app-container">
        {!showResults ? (
          <div className="filter-section">
            <header className="app-header">
              <h1 className="app-title">CollegeFinder</h1>
              <p className="app-subtitle">
                Get colleges tailored to your MHT-CET score
              </p>
            </header>

            {error ? (
              <ErrorMessage
                message={error}
                onRetry={handleRetry}
                type="error"
              />
            ) : isLoading ? (
              <LoadingSpinner
                message="Loading college data..."
                size="large"
              />
            ) : (
              <>
                <ModernFilterForm
                  filters={filters}
                  filterOptions={filterOptions}
                  onFiltersChange={updateFilters}
                  onClearFilters={clearFilters}
                />

                <button
                  className="get-list-btn"
                  onClick={handleGetList}
                  disabled={!filters.minPercentile && !filters.maxPercentile}
                >
                  Get List
                </button>
              </>
            )}

            <footer className="app-footer">
              <p>© 2024 CollegeFinder. All rights reserved.</p>
              <p>Designed with ❤️ by an AI.</p>
            </footer>
          </div>
        ) : (
          <CollegeResults
            colleges={colleges}
            totalResults={totalResults}
            sortConfig={sortConfig}
            onSort={updateSort}
            currentPage={currentPage}
            totalPages={totalPages}
            hasNextPage={hasNextPage}
            hasPrevPage={hasPrevPage}
            onPageChange={goToPage}
            onNextPage={nextPage}
            onPrevPage={prevPage}
            onBackToFilters={handleBackToFilters}
            filters={filters}
          />
        )}
      </div>
    </div>
  );
}

export default App;
