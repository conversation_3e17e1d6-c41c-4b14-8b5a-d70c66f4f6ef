.college-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.results-summary {
  padding: 1rem 1.5rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.results-summary p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.table-wrapper {
  overflow-x: auto;
}

.college-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.college-table th,
.college-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.college-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sortable-header {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.sortable-header:hover {
  background-color: #f3f4f6;
}

.college-table tbody tr:hover {
  background-color: #f9fafb;
}

.college-name {
  min-width: 200px;
}

.college-name strong {
  display: block;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.college-location {
  color: #6b7280;
  font-size: 0.75rem;
}

.category-badge,
.type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.category-badge {
  background-color: #dbeafe;
  color: #1e40af;
}

.category-gopens {
  background-color: #dcfce7;
  color: #166534;
}

.category-gobcs {
  background-color: #fef3c7;
  color: #92400e;
}

.category-gosts {
  background-color: #fce7f3;
  color: #be185d;
}

.category-goschs {
  background-color: #e0e7ff;
  color: #3730a3;
}

.type-government {
  background-color: #dcfce7;
  color: #166534;
}

.type-government-autonomous {
  background-color: #dbeafe;
  color: #1e40af;
}

.type-un-aided {
  background-color: #fef3c7;
  color: #92400e;
}

.type-un-aided-autonomous {
  background-color: #fce7f3;
  color: #be185d;
}

.type-private {
  background-color: #f3e8ff;
  color: #7c3aed;
}

.percentile {
  font-weight: 600;
  color: #1f2937;
}

.rank {
  font-weight: 500;
  color: #374151;
  font-family: "Courier New", monospace;
}

.no-results {
  text-align: center;
  padding: 3rem 1.5rem;
  color: #6b7280;
}

.no-results h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.no-results p {
  margin: 0;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-number {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
}

.page-number:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.page-number.active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .college-table th,
  .college-table td {
    padding: 0.5rem;
  }

  .college-table {
    font-size: 0.75rem;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .pagination-btn,
  .page-number {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
}
