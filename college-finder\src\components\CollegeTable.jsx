/**
 * College table component with sorting and pagination
 * Displays filtered college results in a responsive table format
 */

import PropTypes from 'prop-types';
import './CollegeTable.css';

/**
 * CollegeTable component
 * @param {Object} props - Component props
 * @param {Array} props.colleges - Array of college objects to display
 * @param {Object} props.sortConfig - Current sort configuration
 * @param {Function} props.onSort - Callback for sorting
 * @param {number} props.currentPage - Current page number
 * @param {number} props.totalPages - Total number of pages
 * @param {number} props.totalResults - Total number of results
 * @param {boolean} props.hasNextPage - Whether there's a next page
 * @param {boolean} props.hasPrevPage - Whether there's a previous page
 * @param {Function} props.onPageChange - Callback for page changes
 * @param {Function} props.onNextPage - Callback for next page
 * @param {Function} props.onPrevPage - Callback for previous page
 * @returns {JSX.Element} College table component
 */
const CollegeTable = ({
  colleges,
  sortConfig,
  onSort,
  currentPage,
  totalPages,
  totalResults,
  hasNextPage,
  hasPrevPage,
  onPageChange,
  onNextPage,
  onPrevPage
}) => {
  /**
   * Get sort icon for column header
   * @param {string} field - Field name
   * @returns {string} Sort icon
   */
  const getSortIcon = (field) => {
    if (sortConfig.field !== field) return '↕️';
    return sortConfig.order === 'asc' ? '↑' : '↓';
  };



  /**
   * Generate page numbers for pagination
   * @returns {Array} Array of page numbers to display
   */
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  };

  if (colleges.length === 0) {
    return (
      <div className="college-table-container">
        <div className="no-results">
          <h3>No colleges found</h3>
          <p>Try adjusting your filters to see more results.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="college-table-container">
      {/* Results Summary */}
      <div className="results-summary">
        <p>
          Showing {colleges.length} of {totalResults} colleges 
          (Page {currentPage} of {totalPages})
        </p>
      </div>

      {/* Table */}
      <div className="table-wrapper">
        <table className="college-table">
          <thead>
            <tr>
              <th 
                className="sortable-header"
                onClick={() => onSort('College_Name')}
              >
                College Name {getSortIcon('College_Name')}
              </th>
              <th
                className="sortable-header"
                onClick={() => onSort('Branch_Name')}
              >
                Branch {getSortIcon('Branch_Name')}
              </th>
              <th
                className="sortable-header"
                onClick={() => onSort('Category_Code')}
              >
                Category {getSortIcon('Category_Code')}
              </th>
              <th
                className="sortable-header"
                onClick={() => onSort('Merit_Percentile')}
              >
                Merit % {getSortIcon('Merit_Percentile')}
              </th>
              <th
                className="sortable-header"
                onClick={() => onSort('Cutoff_Rank')}
              >
                Cutoff Rank {getSortIcon('Cutoff_Rank')}
              </th>
              <th
                className="sortable-header"
                onClick={() => onSort('Status')}
              >
                Status {getSortIcon('Status')}
              </th>
              <th
                className="sortable-header"
                onClick={() => onSort('Stage')}
              >
                Stage {getSortIcon('Stage')}
              </th>
            </tr>
          </thead>
          <tbody>
            {colleges.map((college, index) => (
              <tr key={`${college.College_Name}-${college.Branch_Name}-${college.Category_Code}-${index}`}>
                <td className="college-name">
                  <div>
                    <strong>{college.College_Name}</strong>
                    <div className="college-location">{college.Home_University || 'N/A'}</div>
                  </div>
                </td>
                <td>{college.Branch_Name}</td>
                <td>
                  <span className={`category-badge category-${college.Category_Code.toLowerCase()}`}>
                    {college.Category_Code}
                  </span>
                </td>
                <td className="percentile">
                  <strong>{college.Merit_Percentile}%</strong>
                </td>
                <td className="rank">
                  {college.Cutoff_Rank.toLocaleString()}
                </td>
                <td>
                  <span className={`type-badge type-${college.Status.toLowerCase().replace(/\s+/g, '-')}`}>
                    {college.Status}
                  </span>
                </td>
                <td>{college.Stage}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button 
            className="pagination-btn"
            onClick={onPrevPage}
            disabled={!hasPrevPage}
          >
            Previous
          </button>
          
          <div className="page-numbers">
            {getPageNumbers().map(pageNum => (
              <button
                key={pageNum}
                className={`page-number ${pageNum === currentPage ? 'active' : ''}`}
                onClick={() => onPageChange(pageNum)}
              >
                {pageNum}
              </button>
            ))}
          </div>
          
          <button 
            className="pagination-btn"
            onClick={onNextPage}
            disabled={!hasNextPage}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

CollegeTable.propTypes = {
  colleges: PropTypes.array.isRequired,
  sortConfig: PropTypes.object.isRequired,
  onSort: PropTypes.func.isRequired,
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  totalResults: PropTypes.number.isRequired,
  hasNextPage: PropTypes.bool.isRequired,
  hasPrevPage: PropTypes.bool.isRequired,
  onPageChange: PropTypes.func.isRequired,
  onNextPage: PropTypes.func.isRequired,
  onPrevPage: PropTypes.func.isRequired
};

export default CollegeTable;
