/* Main App Styles - Modern Dark Theme */

.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  color: #ffffff;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Filter Section */
.filter-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2rem 0;
  min-height: 100vh;
  overflow: visible;
  position: relative;
}

.app-header {
  text-align: center;
  margin-bottom: 3rem;
}

.app-title {
  color: #ffffff;
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.app-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  font-weight: 400;
  margin: 0;
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.5;
}

/* Get List Button */
.get-list-btn {
  background: #4ecdc4;
  border: none;
  border-radius: 12px;
  color: #1a1a1a;
  padding: 1rem 3rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 2rem auto 0;
  display: block;
  min-width: 200px;
  box-shadow: 0 4px 20px rgba(78, 205, 196, 0.3);
}

.get-list-btn:hover:not(:disabled) {
  background: #45b8b0;
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(78, 205, 196, 0.4);
}

.get-list-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.app-footer {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  line-height: 1.4;
  margin-top: auto;
}

.app-footer p {
  margin: 0.25rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-title {
    font-size: 2.5rem;
  }

  .app-subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .filter-section {
    padding: 1rem 0;
    min-height: auto;
  }

  .app-header {
    margin-bottom: 2rem;
  }

  .get-list-btn {
    padding: 0.875rem 2rem;
    font-size: 1rem;
    min-width: 180px;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 2rem;
  }

  .app-subtitle {
    font-size: 0.9rem;
  }

  .get-list-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    min-width: 160px;
  }
}
