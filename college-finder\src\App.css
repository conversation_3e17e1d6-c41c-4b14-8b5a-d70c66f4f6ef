/* Global App Styles */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-title {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
}

.app-subtitle {
  margin: 0;
  font-size: 1.125rem;
  text-align: center;
  opacity: 0.9;
  font-weight: 300;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 2rem 0;
}

/* Footer */
.app-footer {
  background-color: #374151;
  color: #d1d5db;
  padding: 1rem 0;
  text-align: center;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-title {
    font-size: 2rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .app-header {
    padding: 1.5rem 0;
  }

  .app-main {
    padding: 1rem 0;
  }

  .container {
    padding: 0 0.75rem;
  }
}
