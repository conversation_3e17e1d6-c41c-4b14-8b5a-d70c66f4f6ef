/**
 * Reusable loading spinner component
 * Displays a centered loading animation with optional message
 */

import PropTypes from 'prop-types';
import './LoadingSpinner.css';

/**
 * LoadingSpinner component
 * @param {Object} props - Component props
 * @param {string} props.message - Optional loading message
 * @param {string} props.size - Size of spinner ('small', 'medium', 'large')
 * @returns {JSX.Element} Loading spinner component
 */
const LoadingSpinner = ({ message = 'Loading...', size = 'medium' }) => {
  return (
    <div className="loading-spinner-container">
      <div className={`loading-spinner loading-spinner--${size}`}>
        <div className="spinner"></div>
      </div>
      {message && <p className="loading-message">{message}</p>}
    </div>
  );
};

LoadingSpinner.propTypes = {
  message: PropTypes.string,
  size: PropTypes.oneOf(['small', 'medium', 'large'])
};

export default LoadingSpinner;
