.error-message {
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  border: 1px solid;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-message--error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.error-message--warning {
  background-color: #fffbeb;
  border-color: #fed7aa;
  color: #92400e;
}

.error-message--info {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #1e40af;
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.error-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.error-text {
  flex: 1;
}

.error-title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.error-description {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.retry-button {
  align-self: flex-start;
  background-color: transparent;
  border: 1px solid currentColor;
  color: inherit;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: currentColor;
  color: white;
}

.retry-button:focus {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}
