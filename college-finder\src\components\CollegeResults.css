/* College Results Styles */

.college-results {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.results-header {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 0;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.header-info {
  flex: 1;
}

.results-title {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
}

.results-summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.results-count {
  font-weight: 600;
}

.score-info {
  color: rgba(255, 255, 255, 0.6);
}

.filter-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-tag {
  background: #4ECDC4;
  color: #1a1a1a;
  padding: 0.4rem 0.75rem;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.results-main {
  flex: 1;
  padding: 2rem 0;
}

.results-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* No Results State */
.no-results {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.no-results h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.no-results p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  margin: 0 0 2rem 0;
}

.adjust-filters-btn {
  background: #4ECDC4;
  border: none;
  border-radius: 8px;
  color: #1a1a1a;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.adjust-filters-btn:hover {
  background: #45b8b0;
  transform: translateY(-1px);
}

.results-footer {
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  line-height: 1.4;
}

.footer-content p {
  margin: 0.25rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .filter-summary {
    order: -1;
    width: 100%;
  }
  
  .back-btn {
    align-self: flex-start;
  }
  
  .results-main {
    padding: 1rem 0;
  }
  
  .no-results {
    padding: 3rem 1rem;
  }
  
  .no-results-icon {
    font-size: 3rem;
  }
  
  .no-results h3 {
    font-size: 1.25rem;
  }
}
